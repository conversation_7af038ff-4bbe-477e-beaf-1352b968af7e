package main

import (
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// PDFRequest PDF生成请求结构体
type PDFRequest struct {
	URL         string `json:"url" binding:"required"`         // 要访问的网址
	JWTToken    string `json:"jwt_token,omitempty"`            // JWT认证令牌（可选）
	Fingerprint string `json:"fingerprint,omitempty"`          // 浏览器指纹（可选）
}

// PDFResponse PDF生成响应结构体
type PDFResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message,omitempty"`
	Error   string `json:"error,omitempty"`
}

// PDFHandler PDF处理器
type PDFHandler struct {
	chromeService *chromeService
}

// NewPDFHandler 创建新的PDF处理器
func NewPDFHandler(chromeService *chromeService) *PDFHandler {
	return &PDFHandler{
		chromeService: chromeService,
	}
}

// GeneratePDF 处理PDF生成请求
func (h *PDFHandler) GeneratePDF(c *gin.Context) {
	var req PDFRequest
	
	// 绑定JSON请求体
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, PDFResponse{
			Success: false,
			Error:   fmt.Sprintf("请求参数错误: %v", err),
		})
		return
	}

	// 验证URL格式
	if req.URL == "" {
		c.JSON(http.StatusBadRequest, PDFResponse{
			Success: false,
			Error:   "URL不能为空",
		})
		return
	}

	// 生成PDF
	pdfData, err := h.chromeService.GeneratePDF(req.URL, req.JWTToken, req.Fingerprint)
	if err != nil {
		log.Printf("生成PDF失败: %v", err)
		c.JSON(http.StatusInternalServerError, PDFResponse{
			Success: false,
			Error:   fmt.Sprintf("生成PDF失败: %v", err),
		})
		return
	}

	// 设置响应头
	c.Header("Content-Type", "application/pdf")
	c.Header("Content-Disposition", "attachment; filename=generated.pdf")
	c.Header("Content-Length", fmt.Sprintf("%d", len(pdfData)))

	// 返回PDF数据
	c.Data(http.StatusOK, "application/pdf", pdfData)
}

// HealthCheck 健康检查接口
func (h *PDFHandler) HealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "ok",
		"timestamp": time.Now().Unix(),
		"service":   "pdf-generator",
	})
}

// setupRoutes 设置路由
func setupRoutes(handler *PDFHandler) *gin.Engine {
	// 设置Gin模式
	gin.SetMode(gin.ReleaseMode)
	
	// 创建Gin引擎
	r := gin.Default()

	// 添加CORS中间件
	r.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")
		
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}
		
		c.Next()
	})

	// API路由组
	api := r.Group("/api/v1")
	{
		api.POST("/generate-pdf", handler.GeneratePDF)
		api.GET("/health", handler.HealthCheck)
	}

	// 根路径重定向到健康检查
	r.GET("/", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "/api/v1/health")
	})

	return r
}

func main() {
	// 创建Chrome服务实例，设置60秒超时
	chromeService := newChromeService(60 * time.Second)
	
	// 创建PDF处理器
	pdfHandler := NewPDFHandler(chromeService)
	
	// 设置路由
	router := setupRoutes(pdfHandler)
	
	// 启动服务器
	port := ":8085"
	log.Printf("PDF生成服务启动中...")
	log.Printf("服务地址: http://localhost%s", port)
	log.Printf("API文档:")
	log.Printf("  健康检查: GET  http://localhost%s/api/v1/health", port)
	log.Printf("  生成PDF:  POST http://localhost%s/api/v1/generate-pdf", port)
	log.Printf("请求示例:")
	log.Printf(`  curl -X POST http://localhost%s/api/v1/generate-pdf \
    -H "Content-Type: application/json" \
    -d '{"url":"https://www.example.com"}' \
    --output example.pdf`, port)
	
	if err := router.Run(port); err != nil {
		log.Fatalf("启动服务器失败: %v", err)
	}
}
