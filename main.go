package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"time"

	"github.com/chromedp/cdproto/network"
	"github.com/chromedp/cdproto/page"
	"github.com/chromedp/chromedp"
	"github.com/gin-gonic/gin"
)

// PDFRequest PDF生成请求结构体
type PDFRequest struct {
	URL         string `json:"url" binding:"required"`         // 要访问的网址
	JWTToken    string `json:"jwt_token,omitempty"`            // JWT认证令牌（可选）
	Fingerprint string `json:"fingerprint,omitempty"`          // 浏览器指纹（可选）
}

// PDFResponse PDF生成响应结构体
type PDFResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message,omitempty"`
	Error   string `json:"error,omitempty"`
}

// chromeService Chrome服务结构体
type chromeService struct {
	timeout time.Duration
}

// newChromeService 创建新的Chrome服务实例
func newChromeService(timeout time.Duration) *chromeService {
	return &chromeService{
		timeout: timeout,
	}
}

// createChromeContext 创建Chrome上下文
func (s *chromeService) createChromeContext(ctx context.Context, opts []chromedp.ExecAllocatorOption) (context.Context, func(), error) {
	// 创建执行分配器上下文
	allocCtx, allocCancel := chromedp.NewExecAllocator(ctx, opts...)

	// 创建Chrome上下文
	browserCtx, browserCancel := chromedp.NewContext(allocCtx)

	// 返回清理函数
	cleanup := func() {
		browserCancel()
		allocCancel()
	}

	return browserCtx, cleanup, nil
}

// extractDomain 从URL中提取域名
func extractDomain(rawURL string) string {
	parsedURL, err := url.Parse(rawURL)
	if err != nil {
		return ""
	}

	// 移除端口号
	host := parsedURL.Hostname()
	if host == "" {
		return parsedURL.Host
	}

	return host
}

// GeneratePDF 生成PDF文档
// url: 要访问的网址
// jwtToken: JWT认证令牌
// fingerprint: 浏览器指纹
// 返回: PDF文件的字节数组和错误信息
func (s *chromeService) GeneratePDF(url string, jwtToken string, fingerprint string) ([]byte, error) {
	// 创建Chrome上下文，设置超时
	ctx, cancel := context.WithTimeout(context.Background(), s.timeout)
	defer cancel()

	// 配置Chrome选项
	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.Flag("headless", true),              // 无头模式
		chromedp.Flag("disable-gpu", true),           // 禁用GPU
		chromedp.Flag("no-sandbox", true),            // 禁用沙箱
		chromedp.Flag("disable-dev-shm-usage", true), // 禁用/dev/shm使用
		chromedp.WindowSize(1920, 1080),              // 设置窗口大小
	)

	// 创建Chrome上下文
	browserCtx, cleanup, err := s.createChromeContext(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("创建Chrome上下文失败: %w", err)
	}
	defer cleanup()

	var pdfBuffer []byte

	// 添加panic恢复机制，确保进程清理
	defer func() {
		if r := recover(); r != nil {
			// 发生panic时也要确保进程被清理
			chromedp.Cancel(browserCtx)
		}
	}()

	// 执行浏览器操作
	err = chromedp.Run(browserCtx,
		// 导航到指定URL
		chromedp.Navigate(url),

		// 设置JWT Token Cookie
		chromedp.ActionFunc(func(ctx context.Context) error {
			if jwtToken != "" {
				err := network.SetCookie("token", jwtToken).
					WithDomain(extractDomain(url)).
					WithPath("/").
					WithHTTPOnly(true).
					WithSecure(true).
					WithSameSite(network.CookieSameSiteLax).
					Do(ctx)
				if err != nil {
					return err
				}
			}
			return nil
		}),

		// 设置浏览器指纹Cookie
		chromedp.ActionFunc(func(ctx context.Context) error {
			if fingerprint != "" {
				err := network.SetCookie("FingerPrint", fingerprint).
					WithDomain(extractDomain(url)).
					WithPath("/").
					WithHTTPOnly(true).
					WithSecure(true).
					WithSameSite(network.CookieSameSiteLax).
					Do(ctx)
				if err != nil {
					return err
				}
			}
			return nil
		}),

		// 如果设置了Token或指纹，重新加载页面以应用认证状态
		chromedp.ActionFunc(func(ctx context.Context) error {
			if jwtToken != "" || fingerprint != "" {
				return chromedp.Reload().Do(ctx)
			}
			return nil
		}),

		// 等待页面body元素加载完成
		chromedp.WaitVisible("body", chromedp.ByQuery),

		// 尝试等待markdown-viewer-container元素出现，如果失败则降级到固定等待
		chromedp.ActionFunc(func(ctx context.Context) error {
			// 创建一个较短的超时上下文用于等待特定元素
			waitCtx, waitCancel := context.WithTimeout(ctx, 20*time.Second)
			defer waitCancel()

			// 尝试等待markdown-viewer-container元素
			err := chromedp.WaitVisible(".markdown-viewer-container", chromedp.ByQuery).Do(waitCtx)
			if err != nil {
				// 如果等待失败，使用固定等待时间作为降级策略
				return chromedp.Sleep(3 * time.Second).Do(ctx)
			}
			return nil
		}),

		// 等待额外1秒确保兼容性
		chromedp.Sleep(2*time.Second),

		// 生成PDF
		chromedp.ActionFunc(func(ctx context.Context) error {
			var err error
			pdfBuffer, _, err = page.PrintToPDF().
				WithPrintBackground(true).   // 包含背景色和图片
				WithPreferCSSPageSize(true). // 使用CSS页面大小设置
				WithScale(1.0).              // 缩放比例
				Do(ctx)
			return err
		}),
	)

	if err != nil {
		return nil, fmt.Errorf("生成PDF失败: %w", err)
	}

	if len(pdfBuffer) == 0 {
		return nil, fmt.Errorf("生成的PDF文件为空")
	}

	return pdfBuffer, nil
}

// PDFHandler PDF处理器
type PDFHandler struct {
	chromeService *chromeService
}

// NewPDFHandler 创建新的PDF处理器
func NewPDFHandler(chromeService *chromeService) *PDFHandler {
	return &PDFHandler{
		chromeService: chromeService,
	}
}

// GeneratePDF 处理PDF生成请求
func (h *PDFHandler) GeneratePDF(c *gin.Context) {
	var req PDFRequest
	
	// 绑定JSON请求体
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, PDFResponse{
			Success: false,
			Error:   fmt.Sprintf("请求参数错误: %v", err),
		})
		return
	}

	// 验证URL格式
	if req.URL == "" {
		c.JSON(http.StatusBadRequest, PDFResponse{
			Success: false,
			Error:   "URL不能为空",
		})
		return
	}

	// 生成PDF
	pdfData, err := h.chromeService.GeneratePDF(req.URL, req.JWTToken, req.Fingerprint)
	if err != nil {
		log.Printf("生成PDF失败: %v", err)
		c.JSON(http.StatusInternalServerError, PDFResponse{
			Success: false,
			Error:   fmt.Sprintf("生成PDF失败: %v", err),
		})
		return
	}

	// 设置响应头
	c.Header("Content-Type", "application/pdf")
	c.Header("Content-Disposition", "attachment; filename=generated.pdf")
	c.Header("Content-Length", fmt.Sprintf("%d", len(pdfData)))

	// 返回PDF数据
	c.Data(http.StatusOK, "application/pdf", pdfData)
}

// HealthCheck 健康检查接口
func (h *PDFHandler) HealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "ok",
		"timestamp": time.Now().Unix(),
		"service":   "pdf-generator",
	})
}

// setupRoutes 设置路由
func setupRoutes(handler *PDFHandler) *gin.Engine {
	// 设置Gin模式
	gin.SetMode(gin.ReleaseMode)
	
	// 创建Gin引擎
	r := gin.Default()

	// 添加CORS中间件
	r.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")
		
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}
		
		c.Next()
	})

	// API路由组
	api := r.Group("/api/v1")
	{
		api.POST("/generate-pdf", handler.GeneratePDF)
		api.GET("/health", handler.HealthCheck)
	}

	// 根路径重定向到健康检查
	r.GET("/", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "/api/v1/health")
	})

	return r
}

func main() {
	// 创建Chrome服务实例，设置60秒超时
	chromeService := newChromeService(60 * time.Second)
	
	// 创建PDF处理器
	pdfHandler := NewPDFHandler(chromeService)
	
	// 设置路由
	router := setupRoutes(pdfHandler)
	
	// 启动服务器
	port := ":8085"
	log.Printf("PDF生成服务启动中...")
	log.Printf("服务地址: http://localhost%s", port)
	log.Printf("API文档:")
	log.Printf("  健康检查: GET  http://localhost%s/api/v1/health", port)
	log.Printf("  生成PDF:  POST http://localhost%s/api/v1/generate-pdf", port)
	log.Printf("请求示例:")
	log.Printf(`  curl -X POST http://localhost%s/api/v1/generate-pdf \
    -H "Content-Type: application/json" \
    -d '{"url":"https://www.example.com"}' \
    --output example.pdf`, port)
	
	if err := router.Run(port); err != nil {
		log.Fatalf("启动服务器失败: %v", err)
	}
}
