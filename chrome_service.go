package main

import (
	"context"
	"fmt"
	"net/url"
	"time"

	"github.com/chromedp/cdproto/network"
	"github.com/chromedp/cdproto/page"
	"github.com/chromedp/chromedp"
)

// chromeService Chrome服务结构体
type chromeService struct {
	timeout time.Duration
}

// newChromeService 创建新的Chrome服务实例
func newChromeService(timeout time.Duration) *chromeService {
	return &chromeService{
		timeout: timeout,
	}
}

// createChromeContext 创建Chrome上下文
func (s *chromeService) createChromeContext(ctx context.Context, opts []chromedp.ExecAllocatorOption) (context.Context, func(), error) {
	// 创建执行分配器上下文
	allocCtx, allocCancel := chromedp.NewExecAllocator(ctx, opts...)

	// 创建Chrome上下文
	browserCtx, browserCancel := chromedp.NewContext(allocCtx)

	// 返回清理函数
	cleanup := func() {
		browserCancel()
		allocCancel()
	}

	return browserCtx, cleanup, nil
}

// extractDomain 从URL中提取域名
func extractDomain(rawURL string) string {
	parsedURL, err := url.Parse(rawURL)
	if err != nil {
		return ""
	}

	// 移除端口号
	host := parsedURL.Hostname()
	if host == "" {
		return parsedURL.Host
	}

	return host
}

// GeneratePDF 生成PDF文档
// url: 要访问的网址
// jwtToken: JWT认证令牌
// fingerprint: 浏览器指纹
// 返回: PDF文件的字节数组和错误信息
func (s *chromeService) GeneratePDF(url string, jwtToken string, fingerprint string) ([]byte, error) {
	// 创建Chrome上下文，设置超时
	ctx, cancel := context.WithTimeout(context.Background(), s.timeout)
	defer cancel()

	// 配置Chrome选项
	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.Flag("headless", true),              // 无头模式
		chromedp.Flag("disable-gpu", true),           // 禁用GPU
		chromedp.Flag("no-sandbox", true),            // 禁用沙箱
		chromedp.Flag("disable-dev-shm-usage", true), // 禁用/dev/shm使用
		chromedp.WindowSize(1920, 1080),              // 设置窗口大小
	)

	// 创建Chrome上下文
	browserCtx, cleanup, err := s.createChromeContext(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("创建Chrome上下文失败: %w", err)
	}
	defer cleanup()

	var pdfBuffer []byte

	// 添加panic恢复机制，确保进程清理
	defer func() {
		if r := recover(); r != nil {
			// 发生panic时也要确保进程被清理
			chromedp.Cancel(browserCtx)
		}
	}()

	// 执行浏览器操作
	err = chromedp.Run(browserCtx,
		// 导航到指定URL
		chromedp.Navigate(url),

		// 设置JWT Token Cookie
		chromedp.ActionFunc(func(ctx context.Context) error {
			if jwtToken != "" {
				err := network.SetCookie("token", jwtToken).
					WithDomain(extractDomain(url)).
					WithPath("/").
					WithHTTPOnly(true).
					WithSecure(true).
					WithSameSite(network.CookieSameSiteLax).
					Do(ctx)
				if err != nil {
					return err
				}
			}
			return nil
		}),

		// 设置浏览器指纹Cookie
		chromedp.ActionFunc(func(ctx context.Context) error {
			if fingerprint != "" {
				err := network.SetCookie("FingerPrint", fingerprint).
					WithDomain(extractDomain(url)).
					WithPath("/").
					WithHTTPOnly(true).
					WithSecure(true).
					WithSameSite(network.CookieSameSiteLax).
					Do(ctx)
				if err != nil {
					return err
				}
			}
			return nil
		}),

		// 如果设置了Token或指纹，重新加载页面以应用认证状态
		chromedp.ActionFunc(func(ctx context.Context) error {
			if jwtToken != "" || fingerprint != "" {
				return chromedp.Reload().Do(ctx)
			}
			return nil
		}),

		// 等待页面body元素加载完成
		chromedp.WaitVisible("body", chromedp.ByQuery),

		// 尝试等待markdown-viewer-container元素出现，如果失败则降级到固定等待
		chromedp.ActionFunc(func(ctx context.Context) error {
			// 创建一个较短的超时上下文用于等待特定元素
			waitCtx, waitCancel := context.WithTimeout(ctx, 20*time.Second)
			defer waitCancel()

			// 尝试等待markdown-viewer-container元素
			err := chromedp.WaitVisible(".markdown-viewer-container", chromedp.ByQuery).Do(waitCtx)
			if err != nil {
				// 如果等待失败，使用固定等待时间作为降级策略
				return chromedp.Sleep(3 * time.Second).Do(ctx)
			}
			return nil
		}),

		// 等待额外1秒确保兼容性
		chromedp.Sleep(2*time.Second),

		// 生成PDF
		chromedp.ActionFunc(func(ctx context.Context) error {
			var err error
			pdfBuffer, _, err = page.PrintToPDF().
				WithPrintBackground(true). // 包含背景色和图片
				WithPreferCSSPageSize(true). // 使用CSS页面大小设置
				WithScale(1.0). // 缩放比例
				Do(ctx)
			return err
		}),
	)

	if err != nil {
		return nil, fmt.Errorf("生成PDF失败: %w", err)
	}

	if len(pdfBuffer) == 0 {
		return nil, fmt.Errorf("生成的PDF文件为空")
	}

	return pdfBuffer, nil
}
