#!/bin/bash

echo "=== PDF生成服务测试脚本 ==="
echo

# 检查服务是否运行
echo "1. 检查服务健康状态..."
curl -s http://localhost:8080/api/v1/health | jq . 2>/dev/null || echo "服务未启动或jq未安装"
echo

# 测试PDF生成
echo "2. 测试PDF生成 (example.com)..."
curl -X POST http://localhost:8080/api/v1/generate-pdf \
  -H "Content-Type: application/json" \
  -d '{"url":"https://www.example.com"}' \
  --output test_example.pdf \
  --silent \
  --write-out "HTTP状态码: %{http_code}\n文件大小: %{size_download} bytes\n"

if [ -f "test_example.pdf" ] && [ -s "test_example.pdf" ]; then
    echo "✅ PDF生成成功: test_example.pdf"
    ls -lh test_example.pdf
else
    echo "❌ PDF生成失败"
fi
echo

# 测试带参数的PDF生成
echo "3. 测试带JWT Token的PDF生成..."
curl -X POST http://localhost:8080/api/v1/generate-pdf \
  -H "Content-Type: application/json" \
  -d '{
    "url":"https://httpbin.org/cookies",
    "jwt_token":"test-jwt-token-123",
    "fingerprint":"test-fingerprint-456"
  }' \
  --output test_with_auth.pdf \
  --silent \
  --write-out "HTTP状态码: %{http_code}\n文件大小: %{size_download} bytes\n"

if [ -f "test_with_auth.pdf" ] && [ -s "test_with_auth.pdf" ]; then
    echo "✅ 带认证的PDF生成成功: test_with_auth.pdf"
    ls -lh test_with_auth.pdf
else
    echo "❌ 带认证的PDF生成失败"
fi
echo

# 测试错误处理
echo "4. 测试错误处理 (无效URL)..."
curl -X POST http://localhost:8080/api/v1/generate-pdf \
  -H "Content-Type: application/json" \
  -d '{"url":""}' \
  --silent | jq . 2>/dev/null || echo "返回非JSON响应"
echo

echo "=== 测试完成 ==="
echo "生成的文件:"
ls -la *.pdf 2>/dev/null || echo "没有生成PDF文件"
