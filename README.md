# PDF生成服务

基于Gin框架和ChromeDP的PDF生成HTTP服务，可以将网页转换为PDF文档。

## 功能特性

- 🚀 基于Gin框架的高性能HTTP服务
- 🌐 支持任意网页转PDF
- 🔐 支持JWT Token认证
- 🔒 支持浏览器指纹设置
- 📱 响应式页面支持
- ⚡ 无头Chrome浏览器
- 🛡️ 完善的错误处理和恢复机制

## 快速开始

### 安装依赖

```bash
go mod tidy
```

### 启动服务

```bash
go run .
```

服务将在 `http://localhost:8085` 启动。

## API接口

### 1. 健康检查

```http
GET /api/v1/health
```

**响应示例:**
```json
{
  "status": "ok",
  "timestamp": **********,
  "service": "pdf-generator"
}
```

### 2. 生成PDF

```http
POST /api/v1/generate-pdf
```

**请求体:**
```json
{
  "url": "https://www.example.com",
  "jwt_token": "your-jwt-token-here",
  "fingerprint": "your-browser-fingerprint"
}
```

**参数说明:**
- `url` (必需): 要转换为PDF的网页URL
- `jwt_token` (可选): JWT认证令牌，会设置为cookie
- `fingerprint` (可选): 浏览器指纹，会设置为cookie

**响应:**
- 成功: 返回PDF文件 (Content-Type: application/pdf)
- 失败: 返回JSON错误信息

## 使用示例

### 基本用法

```bash
# 生成简单网页PDF
curl -X POST http://localhost:8085/api/v1/generate-pdf \
  -H "Content-Type: application/json" \
  -d '{"url":"https://www.example.com"}' \
  --output example.pdf
```

### 带认证的请求

```bash
# 带JWT Token和指纹的请求
curl -X POST http://localhost:8085/api/v1/generate-pdf \
  -H "Content-Type: application/json" \
  -d '{
    "url":"https://your-protected-site.com",
    "jwt_token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "fingerprint":"unique-browser-fingerprint"
  }' \
  --output protected-page.pdf
```

### JavaScript示例

```javascript
async function generatePDF(url, jwtToken = '', fingerprint = '') {
  const response = await fetch('http://localhost:8085/api/v1/generate-pdf', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      url: url,
      jwt_token: jwtToken,
      fingerprint: fingerprint
    })
  });

  if (response.ok) {
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'generated.pdf';
    a.click();
  } else {
    const error = await response.json();
    console.error('生成PDF失败:', error);
  }
}

// 使用示例
generatePDF('https://www.example.com');
```

## 配置说明

### Chrome选项
服务使用以下Chrome配置:
- 无头模式 (headless)
- 禁用GPU
- 禁用沙箱
- 窗口大小: 1920x1080
- 超时时间: 60秒

### PDF选项
- 包含背景色和图片
- 使用CSS页面大小设置
- 缩放比例: 1.0

## 错误处理

服务包含完善的错误处理机制:
- 请求参数验证
- Chrome进程管理
- 超时控制
- Panic恢复
- 详细的错误日志

## 部署建议

### Docker部署

可以创建Dockerfile进行容器化部署:

```dockerfile
FROM golang:1.24-alpine AS builder
WORKDIR /app
COPY . .
RUN go mod download
RUN go build -o pdf-service .

FROM chromedp/headless-shell:latest
COPY --from=builder /app/pdf-service /pdf-service
EXPOSE 8080
CMD ["/pdf-service"]
```

### 生产环境配置

- 建议使用反向代理 (Nginx/Apache)
- 配置适当的超时时间
- 监控Chrome进程资源使用
- 设置日志轮转

## 许可证

MIT License
